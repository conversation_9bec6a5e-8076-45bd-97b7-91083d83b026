package staff

import (
	"digital-transformation-api/internal/enum/staff"
	"digital-transformation-api/internal/portal/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"time"
)

type Service interface {
	Create(request *CreateRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateResponse, errs.Error)
	GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error)
	Update(request *UpdateRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateResponse, errs.Error)
	Delete(request *DeleteRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteResponse, errs.Error)
	List(request *ListRequest, rctx *contexts.RouteContext, l logger.Logger) (*ListResponse, errs.Error)
	RegisterLineStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*CheckStaffResponse, errs.Error)
}

// Create Staff
type CreateRequest struct {
	RoleID                *string       `json:"role_id"`
	Permissions           []string      `json:"permissions"`
	EmployeeID            *string       `json:"employee_id"`
	FirstName             string        `json:"firstname" validate:"required"`
	LastName              string        `json:"lastname" validate:"required"`
	Email                 string        `json:"email" validate:"required,email"`
	Password              string        `json:"password" validate:"required,min=8"`
	Phone                 *string       `json:"phone"`
	Gender                *staff.Gender `json:"gender" validate:"omitempty,staff_gender"`
	DateOfBirth           *time.Time    `json:"dateofbirth"`
	Education             *string       `json:"education"`
	Job                   *string       `json:"job"`
	ShopID                *string       `json:"shop_id"`
	AreaID                *string       `json:"area_id"`
	SupervisorID          *string       `json:"supervisor_id"`
	ProfileURL            *string       `json:"profile_url"`
	ProfileURLContentType *string       `json:"profile_url_content_type"`
	ReferenceCode         *string       `json:"reference_code"`
	Status                staff.Status  `json:"status" validate:"required,staff_status"`
}

type CreateResponse struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

// Get Staff by ID
type GetByIDRequest struct {
	ID string `json:"id" validate:"required"`
}

type GetByIDResponse struct {
	Staff *domain.Staff `json:"staff"`
}

// Update Staff
type UpdateRequest struct {
	ID                    string        `json:"id" validate:"required"`
	RoleID                *string       `json:"role_id"`
	Permissions           []string      `json:"permissions"`
	EmployeeID            *string       `json:"employee_id"`
	FirstName             *string       `json:"firstname"`
	LastName              *string       `json:"lastname"`
	Email                 *string       `json:"email" validate:"omitempty,email"`
	Phone                 *string       `json:"phone"`
	Gender                *staff.Gender `json:"gender" validate:"omitempty,staff_gender"`
	DateOfBirth           *time.Time    `json:"dateofbirth"`
	Education             *string       `json:"education"`
	Job                   *string       `json:"job"`
	ShopID                *string       `json:"shop_id"`
	AreaID                *string       `json:"area_id"`
	SupervisorID          *string       `json:"supervisor_id"`
	ProfileURL            *string       `json:"profile_url"`
	ProfileURLContentType *string       `json:"profile_url_content_type"`
	ReferenceCode         *string       `json:"reference_code"`
	Status                *staff.Status `json:"status" validate:"omitempty,staff_status"`
}

type UpdateResponse struct {
	ID        string     `json:"id"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// Delete Staff
type DeleteRequest struct {
	ID string `json:"id" validate:"required"`
}

type DeleteResponse struct {
	ID      string `json:"id"`
	Deleted bool   `json:"deleted"`
}

// List Staff
type ListRequest struct {
	Page     int64         `json:"page" validate:"omitempty,min=1"`
	PageSize int64         `json:"page_size" validate:"omitempty,min=1,max=100"`
	Status   *staff.Status `json:"status"`
	ShopID   *string       `json:"shop_id"`
	AreaID   *string       `json:"area_id"`
	Search   *string       `json:"search"`
}

type ListResponse struct {
	Staff      []domain.Staff `json:"staff"`
	Total      int64          `json:"total"`
	Page       int64          `json:"page"`
	PageSize   int64          `json:"page_size"`
	TotalPages int64          `json:"total_pages"`
}

type RegisterStaffRequest struct {
	EmployeeID string `json:"employee_id" validate:"required"`
	Phone      string `json:"phone" validate:"required"`
}

type GetInfoStaffResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Staff *domain.Staff `json:"staff"`
	} `json:"data"`
	Version string `json:"version"`
}
