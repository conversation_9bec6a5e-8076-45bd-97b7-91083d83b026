package staff

import (
	"math"

	"digital-transformation-api/infrastructure"
	"digital-transformation-api/internal/enum/staff"
	"digital-transformation-api/internal/portal/domain"
	staffdb "digital-transformation-api/internal/portal/port/staff-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/utils"
)

type service struct {
	staffDb staffdb.Port
}

func New(staffDb staffdb.Port) Service {
	return &service{
		staffDb: staffDb,
	}
}

func (s *service) Create(request *CreateRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate create request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Check if email already exists
	existingStaff, err := s.staffDb.GetByEmail(&staffdb.GetByEmailRequest{
		Email: request.Email,
	}, rctx, l)
	if err != nil {
		return nil, err
	}
	if existingStaff.Staff != nil {
		l.<PERSON>("staff email already exists: %s", request.Email)
		return nil, errs.NewBusinessError("40001")
	}

	// Create staff domain object
	staffDomain := &domain.Staff{}
	if err := utils.Scan(request, staffDomain); err != nil {
		l.Errorf("failed to scan request to domain: %v", err)
		return nil, errs.NewInternalError()
	}

	// Set default status if not provided
	if staffDomain.Status == "" {
		staffDomain.Status = staff.StatusActive
	}

	// Create staff in database
	createResp, err := s.staffDb.Create(&staffdb.CreateRequest{
		Staff: staffDomain,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	return &CreateResponse{
		ID:        createResp.Staff.ID,
		CreatedAt: createResp.Staff.CreatedAt,
	}, nil
}

func (s *service) GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate get by id request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	getResp, err := s.staffDb.GetByID(&staffdb.GetByIDRequest{
		ID: request.ID,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	return &GetByIDResponse{
		Staff: getResp.Staff,
	}, nil
}

func (s *service) Update(request *UpdateRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate update request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Get existing staff
	existingResp, err := s.staffDb.GetByID(&staffdb.GetByIDRequest{
		ID: request.ID,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	// Check if email is being changed and already exists
	if request.Email != nil && *request.Email != existingResp.Staff.Email {
		existingEmailStaff, err := s.staffDb.GetByEmail(&staffdb.GetByEmailRequest{
			Email: *request.Email,
		}, rctx, l)
		if err != nil {
			return nil, err
		}
		if existingEmailStaff.Staff != nil {
			l.Errorf("staff email already exists: %s", *request.Email)
			return nil, errs.NewBusinessError("40001")
		}
	}

	// Update staff fields
	updatedStaff := existingResp.Staff
	if request.RoleID != nil {
		updatedStaff.RoleID = request.RoleID
	}
	if request.Permissions != nil {
		updatedStaff.Permissions = request.Permissions
	}
	if request.EmployeeID != nil {
		updatedStaff.EmployeeID = request.EmployeeID
	}
	if request.FirstName != nil {
		updatedStaff.FirstName = *request.FirstName
	}
	if request.LastName != nil {
		updatedStaff.LastName = *request.LastName
	}
	if request.Email != nil {
		updatedStaff.Email = *request.Email
	}
	if request.Phone != nil {
		updatedStaff.Phone = request.Phone
	}
	if request.Gender != nil {
		updatedStaff.Gender = request.Gender
	}
	if request.DateOfBirth != nil {
		updatedStaff.DateOfBirth = request.DateOfBirth
	}
	if request.Education != nil {
		updatedStaff.Education = request.Education
	}
	if request.Job != nil {
		updatedStaff.Job = request.Job
	}
	if request.ShopID != nil {
		updatedStaff.ShopID = request.ShopID
	}
	if request.AreaID != nil {
		updatedStaff.AreaID = request.AreaID
	}
	if request.SupervisorID != nil {
		updatedStaff.SupervisorID = request.SupervisorID
	}
	if request.ProfileURL != nil {
		updatedStaff.ProfileURL = request.ProfileURL
	}
	if request.ProfileURLContentType != nil {
		updatedStaff.ProfileURLContentType = request.ProfileURLContentType
	}
	if request.ReferenceCode != nil {
		updatedStaff.ReferenceCode = request.ReferenceCode
	}
	if request.Status != nil {
		updatedStaff.Status = *request.Status
	}

	// Update staff in database
	updateResp, err := s.staffDb.Update(&staffdb.UpdateRequest{
		Staff: updatedStaff,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	return &UpdateResponse{
		ID:        updateResp.Staff.ID,
		UpdatedAt: updateResp.Staff.UpdatedAt,
	}, nil
}

func (s *service) Delete(request *DeleteRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate delete request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	deleteResp, err := s.staffDb.Delete(&staffdb.DeleteRequest{
		ID: request.ID,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	return &DeleteResponse{
		ID:      request.ID,
		Deleted: deleteResp.Success,
	}, nil
}

func (s *service) List(request *ListRequest, rctx *contexts.RouteContext, l logger.Logger) (*ListResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate list request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Set default pagination
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 {
		request.PageSize = 10
	}
	if request.PageSize > 100 {
		request.PageSize = 100
	}

	// Get staff list
	listResp, err := s.staffDb.List(&staffdb.ListRequest{
		Page:     request.Page,
		PageSize: request.PageSize,
		Status:   request.Status,
		ShopID:   request.ShopID,
		AreaID:   request.AreaID,
		Search:   request.Search,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	// Get total count
	countResp, err := s.staffDb.Count(&staffdb.CountRequest{
		Status: request.Status,
		ShopID: request.ShopID,
		AreaID: request.AreaID,
		Search: request.Search,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	totalPages := int64(math.Ceil(float64(countResp.Count) / float64(request.PageSize)))

	return &ListResponse{
		Staff:      listResp.Staff,
		Total:      countResp.Count,
		Page:       request.Page,
		PageSize:   request.PageSize,
		TotalPages: totalPages,
	}, nil
}

func (s *service) RegisterLineStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetInfoStaffResponse, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate register staff request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	userLineId := rctx.Header.TokenLine
	if userLineId == "" {
		return nil, errs.NewCustom(401, "40001", "User id is required", "User id is required")
	}

	// Get staff by employee_id and phone
	getResp, err := s.staffDb.GetByEmployeeIDAndPhone(&staffdb.GetByEmployeeIDAndPhoneRequest{
		EmployeeID: request.EmployeeID,
		Phone:      request.Phone,
	}, rctx, l)
	if err != nil {
		return nil, err
	}

	// Get staff token by user_line_id
	tokenResp, err := s.staffDb.GetByUserTokenOrStaffId(&staffdb.GetByUserTokenOrStaffIdRequest{
		UserToken: userLineId,
	}, rctx, l)
	if err != nil {
		return nil, err
	}
	l.Info("tokenResp StaffToken get")
	l.Info(tokenResp.StaffToken)

	if getResp.Staff == nil {
		return nil, errs.NewCustom(400, "40002", "เข้าสู่ระบบไม่สำเร็จ", "เข้าสู่ระบบไม่สำเร็จ")
	}

	return &GetInfoStaffResponse{
		Success: true,
		Data: struct {
			Staff *domain.Staff `json:"staff"`
		}{
			Staff: getResp.Staff,
		},
		Version: "1.0.0",
	}, nil
}

// GetStaffInfo retrieves staff information using LINE token from header
func (s *service) GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetStaffInfoResponse, errs.Error) {
	// Get LINE token from header
	lineToken := rctx.Header.TokenLine
	if lineToken == "" {
		l.Errorf("LINE token is required")
		return &GetStaffInfoResponse{
			Success: false,
			Message: "LINE token is required",
			Version: "1.0.0",
		}, errs.NewCustom(401, "40001", "LINE token is required", "LINE token is required")
	}

	// Get staff token by LINE token
	tokenResp, err := s.staffDb.GetByUserTokenOrStaffId(&staffdb.GetByUserTokenOrStaffIdRequest{
		UserToken: lineToken,
	}, rctx, l)
	if err != nil {
		l.Errorf("failed to get staff token: %v", err)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Failed to retrieve staff information",
			Version: "1.0.0",
		}, err
	}

	if tokenResp.StaffToken == nil {
		l.Errorf("staff token not found for LINE token")
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Invalid LINE token",
			Version: "1.0.0",
		}, errs.NewCustom(404, "40404", "Staff not found", "Staff not found")
	}

	// Get staff by staff_id from token
	staffResp, err := s.staffDb.GetByID(&staffdb.GetByIDRequest{
		ID: tokenResp.StaffToken.StaffID,
	}, rctx, l)
	if err != nil {
		l.Errorf("failed to get staff by ID: %v", err)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Failed to retrieve staff information",
			Version: "1.0.0",
		}, err
	}

	if staffResp.Staff == nil {
		l.Errorf("staff not found for ID: %s", tokenResp.StaffToken.StaffID)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Staff not found",
			Version: "1.0.0",
		}, errs.NewCustom(404, "40404", "Staff not found", "Staff not found")
	}

	return &GetStaffInfoResponse{
		Success: true,
		Data: &StaffInfoData{
			Staff: staffResp.Staff,
		},
		Version: "1.0.0",
	}, nil
}