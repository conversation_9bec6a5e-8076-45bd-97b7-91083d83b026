package handler

import (
	"net/http"

	"digital-transformation-api/infrastructure"
	staffdb "digital-transformation-api/internal/portal/port/staff-db"
	"digital-transformation-api/internal/line/service/staff"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"

	"github.com/gin-gonic/gin"
)

type staffHandler struct {
	service staff.Service
}

func NewStaffHandler(service staff.Service) *staffHandler {
	return &staffHandler{
		service: service,
	}
}

// Register Staff With LineUserId
func (h *staffHandler) RegisterLineUser(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request staff.RegisterStaffRequest
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind check staff request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	response, err := h.service.RegisterLineStaff(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// GetStaffInfo retrieves staff information using LINE token from header
func (h *staffHandler) GetStaffInfo(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	// Create empty request since token comes from header
	request := &staff.GetStaffInfoRequest{}

	response, err := h.service.GetStaffInfo(request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Bind Staff Routes
func BindStaffRoute(app gins.GinApps) {
	svc := staff.New(
		staffdb.NewAdaptorPG(infrastructure.Db),
	)

	hdl := NewStaffHandler(svc)

	app.Register(http.MethodPost, "/line/staff/register", app.ParseRouteContext(hdl.RegisterLineUser))
	app.Register(http.MethodGet, "/line/staff/get-info", app.ParseRouteContext(hdl.GetStaffInfo))
}